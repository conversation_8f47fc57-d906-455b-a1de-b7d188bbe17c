<html lang='zh-CN'>
        <head >
        <meta charset='utf-8'/>
<meta content='width=device-width, initial-scale=1.0' name='viewport'/>
<title >声线 - AI声音克隆合成创作工具</title>
<script src='https://res.gemcoder.com/js/reload.js'></script>
<script src='https://cdn.tailwindcss.com'></script>
<link href='https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'/>
<script >tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366F1', // 主色调：靛蓝色，代表科技感和创新
                        secondary: '#EC4899', // 辅助色：粉色，代表创意和活力
                        dark: '#1E293B', // 深色文本
                        light: '#F8FAFC', // 浅色背景
                        muted: '#94A3B8', // 次要文本
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }</script>
<style type='text/tailwindcss'>
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .text-gradient {
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;
            }
            .bg-glass {
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
            }
            .animate-float {
                animation: float 6s ease-in-out infinite;
            }
            .animate-pulse-slow {
                animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
            }
            .scrollbar-hide::-webkit-scrollbar {
                display: none;
            }
            .scrollbar-hide {
                -ms-overflow-style: none;
                scrollbar-width: none;
            }
        }
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }
    </style>
    </head>
<body class='font-sans bg-light text-dark antialiased'>
        <!-- 导航栏 -->
<header class='fixed w-full top-0 z-50 transition-all duration-300 bg-transparent' id='navbar'>
        <div class='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div class='flex justify-between items-center py-4'>
        <div class='flex items-center'>
        <div class='text-primary text-2xl font-bold flex items-center'>
        <i class='fas fa-microphone-alt mr-2'>
        </i>
<span >
        声线
    </span>
    </div>
    </div>
<!-- 桌面导航 -->
<nav class='hidden md:flex space-x-8' data-ytextravalue='extra-wduybiimp'>
        </nav>
<div class='flex items-center space-x-4'>
        <a class='px-5 py-2 rounded-full bg-primary text-white hover:bg-primary/90 shadow-lg shadow-primary/20 transition-all transform hover:-translate-y-0.5' data-yteditvalue='下载APP' href='javascript:void(0);'>
        下载APP
    </a>
<!-- 移动端菜单按钮 -->
<button class='md:hidden text-dark focus:outline-none' id='mobile-menu-button'>
        <i class='fas fa-bars text-xl'>
        </i>
    </button>
    </div>
    </div>
    </div>
<!-- 移动端导航菜单 -->
<div class='hidden md:hidden bg-white shadow-lg absolute w-full' id='mobile-menu'>
        <div class='container mx-auto px-4 py-3 space-y-3'>
        <a class='block py-2 text-dark hover:text-primary transition-colors font-medium' href='#features'>
        功能特点
    </a>
<a class='block py-2 text-dark hover:text-primary transition-colors font-medium' href='#scenarios'>
        应用场景
    </a>
<a class='block py-2 text-dark hover:text-primary transition-colors font-medium' href='#how-it-works'>
        使用方法
    </a>
<a class='block py-2 text-dark hover:text-primary transition-colors font-medium' href='#testimonials'>
        用户评价
    </a>
<a class='block py-2 text-dark hover:text-primary transition-colors font-medium' href='#faq'>
        常见问题
    </a>
<a class='block py-2 text-dark hover:text-primary transition-colors font-medium' href='javascript:void(0);'>
        登录
    </a>
    </div>
    </div>
    </header>
<main >
        <!-- 英雄区域 -->
<section class='pt-32 pb-20 md:pt-40 md:pb-32 overflow-hidden relative'>
        <div class='absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 -z-10'>
        </div>
<!-- 装饰元素 -->
<div class='absolute top-20 right-10 w-64 h-64 bg-primary/10 rounded-full filter blur-3xl'>
        </div>
<div class='absolute bottom-20 left-10 w-80 h-80 bg-secondary/10 rounded-full filter blur-3xl'>
        </div>
<div class='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div class='flex flex-col lg:flex-row items-center'>
        <div class='lg:w-1/2 mb-12 lg:mb-0'>
        <div class='inline-block px-4 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium mb-6'>
        <i class='fas fa-rocket mr-2'>
        </i>
AI声音创作新体验
    </div>
<h1 class='text-[clamp(2.5rem,5vw,4rem)] font-bold leading-tight mb-6'>
        让AI用
<span class='bg-gradient-to-r from-primary to-secondary text-gradient'>
        你的声音
    </span>
把文字读出来
    </h1>
<p class='text-lg md:text-xl text-gray-600 mb-8 max-w-xl'>
        3秒钟复刻你的声音，文字秒变语音，1分钟读完1小时内容。完美复刻本人声线，无论是短视频配音、播客制作还是有声书创作，都能轻松搞定。
    </p>
<div class='flex flex-col sm:flex-row gap-4 mb-10'>
        </div>
    </div>
<div class='lg:w-1/2 relative'>
        <div class='relative z-10 animate-float'>
        <div class='relative mx-auto'>
        <!-- 手机框架 -->
<div class='w-[320px] h-[640px] mx-auto border-8 border-gray-800 rounded-[36px] shadow-2xl bg-gray-900 relative overflow-hidden'>
        <!-- 手机顶部听筒 -->
<div class='absolute top-4 left-1/2 transform -translate-x-1/2 w-24 h-4 bg-gray-800 rounded-full'>
        </div>
<!-- 缩小后的应用界面 -->
<img alt='声线AI声音工具界面' class='absolute inset-0 w-[90%] h-[85%] mx-auto my-[7%] object-cover rounded-[16px]' src='https:/design.gemcoder.com/api/upload/appResource/HysdBNIrAYlH1HakLtpj5.jpg'/>
    </div>
    </div>
<!-- 浮动元素1 -->
<div class='absolute -top-6 -left-6 bg-white p-4 rounded-xl shadow-lg animate-pulse-slow'>
        <div class='flex items-center gap-3'>
        <div class='w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-500'>
        <i class='fas fa-check text-xl'>
        </i>
    </div>
<div >
        <p class='font-medium'>
        克隆完成
    </p>
<p class='text-sm text-gray-500'>
        相似度99.8%
    </p>
    </div>
    </div>
    </div>
<!-- 浮动元素2 -->
<div class='absolute -bottom-8 -right-8 bg-white p-4 rounded-xl shadow-lg animate-pulse-slow' style='animation-delay: 1s;'>
        <div class='flex items-center gap-3'>
        <div class='w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500'>
        <i class='fas fa-clock text-xl'>
        </i>
    </div>
<div >
        <p class='font-medium'>
        处理速度
    </p>
<p class='text-sm text-gray-500'>
        1分钟/小时内容
    </p>
    </div>
    </div>
    </div>
    </div>
<!-- 背景装饰 -->
<div class='absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-r from-primary/30 to-secondary/30 rounded-full filter blur-3xl -z-10'>
        </div>
    </div>
    </div>
    </div>
    </section>
<!-- 功能特点 -->
<section class='py-20 bg-white' id='features'>
        <div class='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div class='text-center max-w-3xl mx-auto mb-16'>
        <div class='inline-block px-4 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4'>
        <i class='fas fa-crown mr-2'>
        </i>
核心功能
    </div>
<h2 class='text-[clamp(1.8rem,3vw,2.5rem)] font-bold mb-6'>
        为什么选择声线AI声音工具
    </h2>
<p class='text-lg text-gray-600'>
        我们的AI声音克隆合成技术，让声音创作变得前所未有的简单高效
    </p>
    </div>
<div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
        <!-- 功能卡片1 -->
<div class='bg-gray-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
        <div class='w-14 h-14 rounded-xl bg-primary/10 flex items-center justify-center text-primary text-2xl mb-6'>
        <i class='fas fa-magic'>
        </i>
    </div>
<h3 class='text-xl font-bold mb-3'>
        克隆快又准
    </h3>
<p class='text-gray-600'>
        3秒钟复刻你的声音语气、情感细节全捕捉，相似度99%+，让AI成为你的声音分身。
    </p>
    </div>
<!-- 功能卡片2 -->
<div class='bg-gray-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
        <div class='w-14 h-14 rounded-xl bg-secondary/10 flex items-center justify-center text-secondary text-2xl mb-6'>
        <i class='fas fa-file-alt'>
        </i>
    </div>
<h3 class='text-xl font-bold mb-3'>
        文字转语音超方便
    </h3>
<p class='text-gray-600'>
        支持PDF、TXT一键提取文字，直接转音频，不用手动打字，效率翻倍，告别繁琐输入。
    </p>
    </div>
<!-- 功能卡片3 -->
<div class='bg-gray-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
        <div class='w-14 h-14 rounded-xl bg-green-100 flex items-center justify-center text-green-500 text-2xl mb-6'>
        <i class='fas fa-th-large'>
        </i>
    </div>
<h3 class='text-xl font-bold mb-3'>
        多场景全覆盖
    </h3>
<p class='text-gray-600'>
        短视频配音、课程录制、有声书创作、企业播报... 生成内容无上限，长文本也能轻松搞定。
    </p>
    </div>
<!-- 功能卡片4 -->
<div class='bg-gray-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'>
        <div class='w-14 h-14 rounded-xl bg-blue-100 flex items-center justify-center text-blue-500 text-2xl mb-6'>
        <i class='fas fa-share-alt'>
        </i>
    </div>
<h3 class='text-xl font-bold mb-3'>
        一键分享不费力
    </h3>
<p class='text-gray-600'>
        生成的音频文件支持直接同步到社交平台，内容传播快人一步，让你的声音作品迅速扩散。
    </p>
    </div>
    </div>
<!-- 详细功能展示 -->
<div class='mt-24 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center'>
        <div class='order-2 lg:order-1'>
        <h3 class='text-3xl font-bold mb-6'>
        完美复刻你的声音，情感细节全捕捉
    </h3>
<p class='text-gray-600 mb-8'>
        我们的AI声音克隆技术不仅能复制你的音色，还能精准捕捉语气、节奏和情感变化，让生成的语音听起来自然真实，就像你亲自朗读一样。
    </p>
<div class='space-y-6'>
        <div class='flex items-start gap-4'>
        <div class='w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-500 mt-1 flex-shrink-0'>
        <i class='fas fa-check'>
        </i>
    </div>
<div >
        <h4 class='font-bold text-lg mb-1'>
        99%+相似度
    </h4>
<p class='text-gray-600'>
        专业级声音克隆，朋友也分辨不出差异
    </p>
    </div>
    </div>
<div class='flex items-start gap-4'>
        <div class='w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-500 mt-1 flex-shrink-0'>
        <i class='fas fa-check'>
        </i>
    </div>
<div >
        <h4 class='font-bold text-lg mb-1'>
        情感丰富
    </h4>
<p class='text-gray-600'>
        支持喜悦、悲伤、愤怒等多种情感表达
    </p>
    </div>
    </div>
<div class='flex items-start gap-4'>
        <div class='w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-500 mt-1 flex-shrink-0'>
        <i class='fas fa-check'>
        </i>
    </div>
<div >
        <h4 class='font-bold text-lg mb-1'>
        多风格转换
    </h4>
<p class='text-gray-600'>
        支持新闻播报、故事讲述、广告宣传等多种风格
    </p>
    </div>
    </div>
    </div>
    </div>
<div class='order-1 lg:order-2 relative'>
        <div class='relative z-10 rounded-2xl overflow-hidden shadow-2xl'>
        <!-- 波形动画 -->
<div class='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6'>
        <div class='flex items-end justify-center gap-1 h-24'>
        <div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 40%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 60%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 30%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 70%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 50%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 80%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 40%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 60%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 90%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 50%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 30%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 70%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 50%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 60%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 40%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 80%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 50%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 30%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 60%'>
        </div>
<div class='w-1 bg-white/70 rounded-t-sm animate-pulse' style='height: 40%'>
        </div>
    </div>
    </div>
    </div>
<!-- 装饰元素 -->
<div class='absolute -top-6 -right-6 w-32 h-32 bg-primary/20 rounded-full filter blur-2xl -z-10'>
        </div>
<div class='absolute -bottom-6 -left-6 w-32 h-32 bg-secondary/20 rounded-full filter blur-2xl -z-10'>
        </div>
    </div>
    </div>
<!-- 第二个详细功能 -->
<div class='mt-24 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center'>
        <div class='relative'>
        <div class='relative z-10 rounded-2xl overflow-hidden shadow-2xl'>
        </div>
<!-- 装饰元素 -->
<div class='absolute -top-6 -left-6 w-32 h-32 bg-green-100 rounded-full filter blur-2xl -z-10'>
        </div>
<div class='absolute -bottom-6 -right-6 w-32 h-32 bg-blue-100 rounded-full filter blur-2xl -z-10'>
        </div>
    </div>
<div >
        <h3 class='text-3xl font-bold mb-6'>
        多种文件格式支持，告别手动输入
    </h3>
<p class='text-gray-600 mb-8' data-yteditvalue='无论是PDF电子书还是TXT文档，声线都能一键提取文字内容，直接转换为语音，让你告别繁琐的复制粘贴和手动输入。'>
        无论是PDF电子书还是TXT文档，声线都能一键提取文字内容，直接转换为语音，让你告别繁琐的复制粘贴和手动输入。
    </p>
<div class='grid grid-cols-2 gap-4 mb-8'>
        <div class='bg-gray-50 p-4 rounded-xl flex items-center gap-3'>
        <i class='fas fa-file-pdf text-red-500 text-xl'>
        </i>
<span >
        PDF文档
    </span>
    </div>
<div class='bg-gray-50 p-4 rounded-xl flex items-center gap-3'>
        <i class='fas fa-file-alt text-blue-500 text-xl'>
        </i>
<span >
        TXT文本
    </span>
    </div>
<div class='bg-gray-50 p-4 rounded-xl flex items-center gap-3' data-selectorname='#id-2ujf2' style>
        <img alt='Word文档' class='w-6 h-6 object-cover' src='https://design.gemcoder.com/staticResource/echoAiSystemImages/a892c2d7a3293eb55689b4c19f6c2643.png'/>
<span data-yteditvalue='图片（开发中）'>
        图片（开发中）
    </span>
    </div>
<div class='bg-gray-50 p-4 rounded-xl flex items-center gap-3'>
        <i class='fas fa-link text-green-500 text-xl'>
        </i>
<span data-yteditvalue='网页链接（开发中）'>
        网页链接（开发中）
    </span>
    </div>
    </div>
    </div>
    </div>
    </div>
    </section>
<!-- 使用方法 -->
<section class='py-20 bg-white' id='how-it-works'>
        <div class='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div class='text-center max-w-3xl mx-auto mb-16'>
        <div class='inline-block px-4 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4'>
        <i class='fas fa-route mr-2'>
        </i>
使用流程
    </div>
<h2 class='text-[clamp(1.8rem,3vw,2.5rem)] font-bold mb-6'>
        3步轻松完成声音创作
    </h2>
<p class='text-lg text-gray-600'>
        简单几步，让AI用你的声音把文字读出来，高效完成声音内容创作
    </p>
    </div>
<div class='relative'>
        <!-- 连接线 -->
<div class='hidden md:block absolute top-1/2 left-0 right-0 h-1 bg-gray-200 -translate-y-1/2 z-0'>
        </div>
<div class='grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10'>
        <!-- 步骤1 -->
<div class='flex flex-col items-center text-center'>
        <div class='w-20 h-20 rounded-full bg-primary text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-lg shadow-primary/20'>
        1
    </div>
<div class='bg-gray-50 p-6 rounded-2xl w-full'>
        <h3 class='text-xl font-bold mb-3'>
        克隆你的声音
    </h3>
<p class='text-gray-600 mb-4'>
        录制3秒钟音频，AI将精准克隆你的声音特征、语气和情感。
    </p>
<img alt='声音克隆步骤' class='w-full h-auto rounded-lg mb-4' src='https://design.gemcoder.com/staticResource/echoAiSystemImages/a8da0efba9a91d1125aef06481d281a1.png'/>
<span class='text-sm text-gray-500'>
        只需3秒，相似度高达99%+
    </span>
    </div>
    </div>
<!-- 步骤2 -->
<div class='flex flex-col items-center text-center mt-12 md:mt-0'>
        <div class='w-20 h-20 rounded-full bg-primary text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-lg shadow-primary/20'>
        2
    </div>
<div class='bg-gray-50 p-6 rounded-2xl w-full'>
        <h3 class='text-xl font-bold mb-3'>
        输入文字内容
    </h3>
<p class='text-gray-600 mb-4'>
        直接输入文字或上传文档，支持PDF、TXT等多种格式一键转换。
    </p>
<img alt='输入文字步骤' class='w-full h-auto rounded-lg mb-4' src='https://design.gemcoder.com/staticResource/echoAiSystemImages/dd1cca3fb55a3e3fe273111c065bbdd1.png'/>
<span class='text-sm text-gray-500'>
        支持OCR识别图片中的文字
    </span>
    </div>
    </div>
<!-- 步骤3 -->
<div class='flex flex-col items-center text-center mt-12 md:mt-0'>
        <div class='w-20 h-20 rounded-full bg-primary text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-lg shadow-primary/20'>
        3
    </div>
<div class='bg-gray-50 p-6 rounded-2xl w-full'>
        <h3 class='text-xl font-bold mb-3'>
        生成与分享
    </h3>
<p class='text-gray-600 mb-4'>
        选择声音风格和语速，一键生成音频，支持直接分享到社交平台。
    </p>
<img alt='生成分享步骤' class='w-full h-auto rounded-lg mb-4' src='https://design.gemcoder.com/staticResource/echoAiSystemImages/4239c45539e3ad787a21cbe22d50a21f.png'/>
<span class='text-sm text-gray-500'>
        多种格式导出，支持无损音质
    </span>
    </div>
    </div>
    </div>
    </div>
<!-- 快速开始 -->
<div class='mt-20 bg-gradient-to-r from-primary to-secondary rounded-3xl overflow-hidden shadow-2xl'>
        <div class='grid grid-cols-1 lg:grid-cols-2 items-center'>
        <div class='hidden lg:block relative h-full min-h-[300px]'>
        <img alt='开始使用声线' class='absolute inset-0 w-full h-full object-cover' src='https://design.gemcoder.com/staticResource/echoAiSystemImages/d4e71db0106e938ce07c81db7158e0bb.png'/>
    </div>
    </div>
    </div>
    </div>
    </section>
<!-- 常见问题 -->
<section class='py-20 bg-white' id='faq'>
        <div class='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div class='text-center max-w-3xl mx-auto mb-16'>
        </div>
    </div>
    </section>
    </main>
<!-- 页脚 -->
<footer class='bg-gray-900 text-white pt-16 pb-8'>
        <div class='container mx-auto px-4 sm:px-6 lg:px-8'>
        <div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12 mb-12'>
        <div class='lg:col-span-2'>
        <div class='text-2xl font-bold flex items-center mb-6'>
        <i class='fas fa-microphone-alt mr-2 text-primary'>
        </i>
<span >
        声线
    </span>
    </div>
<p class='text-gray-400 mb-6 max-w-md'>
        声线是一款专业AI声音克隆合成创作工具，支持快速精准克隆声音音色，把文本内容转成语音，满足多场景声音创作需求。
    </p>
    </div>
<div >
        </div>
    </div>
<div class='border-t border-gray-800 pt-8'>
        <div class='flex flex-col md:flex-row justify-between items-center' data-ytextravalue='extra-hc7l3ig3v'>
        <p class='text-gray-500 text-sm mb-4 md:mb-0' data-ytparentvalue='extra-hc7l3ig3v' data-ytoriginindex='0' data-ytindex='0' data-yteditvalue='© 2025 声线 AI 声音工具. 保留所有权利.'>
        © 2025 声线 AI 声音工具. 保留所有权利.
    </p>
<div class='flex space-x-6' data-ytoriginindex='1' data-ytindex='1'>
        </div>
    </div>
    </div>
    </div>
    </footer>
<!-- 返回顶部按钮 -->
<button class='fixed bottom-8 right-8 w-12 h-12 rounded-full bg-primary text-white shadow-lg flex items-center justify-center opacity-0 invisible transition-all duration-300 hover:bg-primary/90' id='back-to-top'>
        <i class='fas fa-arrow-up'>
        </i>
    </button>
<script >// 页面加载时初始化
        document.addEventListener('DOMContentLoaded', () => {
            // 导航栏滚动效果
            const navbar = document.getElementById('navbar');
            window.addEventListener('scroll', () => {
                if (window.scrollY > 50) {
                    navbar.classList.add('bg-white', 'shadow-md');
                    navbar.classList.remove('bg-transparent');
                } else {
                    navbar.classList.remove('bg-white', 'shadow-md');
                    navbar.classList.add('bg-transparent');
                }
            });
            
            // 移动端菜单切换
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
                const icon = mobileMenuButton.querySelector('i');
                if (mobileMenu.classList.contains('hidden')) {
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-bars');
                } else {
                    icon.classList.remove('fa-bars');
                    icon.classList.add('fa-times');
                }
            });
            
            // 平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 关闭移动菜单（如果打开）
                    if (!mobileMenu.classList.contains('hidden')) {
                        mobileMenu.classList.add('hidden');
                        const icon = mobileMenuButton.querySelector('i');
                        icon.classList.remove('fa-times');
                        icon.classList.add('fa-bars');
                    }
                    
                    const targetId = this.getAttribute('href');
                    if (targetId === '#') return;
                    
                    const targetElement = document.querySelector(targetId);
                    if (targetElement) {
                        window.scrollTo({
                            top: targetElement.offsetTop - 80,
                            behavior: 'smooth'
                        });
                    }
                });
            });
            
            // FAQ 切换
            const faqToggles = document.querySelectorAll('.faq-toggle');
            faqToggles.forEach(toggle => {
toggle.addEventListener('click', () => {                    const content = toggle.nextElementSibling;
                    const icon = toggle.querySelector('i');
                    
                    // 关闭其他所有FAQ
                    document.querySelectorAll('.faq-content').forEach(item => {
                        if (item !== content && !item.classList.contains('hidden')) {
                            item.classList.add('hidden');
                            item.previousElementSibling.querySelector('i').classList.remove('rotate-180');
                        }
                    });
                    
                    // 切换当前FAQ
                    content.classList.toggle('hidden');
                    icon.classList.toggle('rotate-180');
                });
            });
            
            // 返回顶部按钮
            const backToTopButton = document.getElementById('back-to-top');
            
            window.addEventListener('scroll', () => {
                if (window.scrollY > 300) {
                    backToTopButton.classList.remove('opacity-0', 'invisible');
                    backToTopButton.classList.add('opacity-100', 'visible');
                } else {
                    backToTopButton.classList.add('opacity-0', 'invisible');
                    backToTopButton.classList.remove('opacity-100', 'visible');
                }
            });
            
            backToTopButton.addEventListener('click', () => {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
            
            // 数字增长动画
            function animateValue(id, start, end, duration) {
                const obj = document.getElementById(id);
                if (!obj) return;
                
                let startTimestamp = null;
                const step = (timestamp) => {
                    if (!startTimestamp) startTimestamp = timestamp;
                    const progress = Math.min((timestamp - startTimestamp) / duration, 1);
                    
                    if (id === 'satisfaction') {
                        obj.innerHTML = Math.floor(progress * (end - start) + start) + '%';
                    } else {
                        // 格式化数字为带逗号的格式
                        obj.innerHTML = Math.floor(progress * (end - start) + start).toLocaleString();
                        if (id !== 'user-count' && id !== 'audio-count' && id !== 'time-saved') {
                            obj.innerHTML += '+';
                        }
                    }
                    
                    if (progress < 1) {
                        window.requestAnimationFrame(step);
                    }
                };
                window.requestAnimationFrame(step);
            }
            

        });</script>
    </body>
    </html>