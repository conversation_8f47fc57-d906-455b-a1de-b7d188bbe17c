import React from 'react';

const HowItWorks: React.FC = () => {
  const steps = [
    {
      number: 1,
      title: '克隆你的声音',
      description: '录制3秒钟音频，AI将精准克隆你的声音特征、语气和情感。',
      image: '/images/step1-clone.png',
      note: '只需3秒，相似度高达99%+'
    },
    {
      number: 2,
      title: '输入文字内容',
      description: '直接输入文字或上传文档，支持PDF、TXT等多种格式一键转换。',
      image: '/images/step2-input.png',
      note: '支持OCR识别图片中的文字'
    },
    {
      number: 3,
      title: '生成与分享',
      description: '选择声音风格和语速，一键生成音频，支持直接分享到社交平台。',
      image: '/images/step3-generate.png',
      note: '多种格式导出，支持无损音质'
    }
  ];

  return (
    <section className="py-20 bg-white" id="how-it-works">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="inline-block px-4 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
            <i className="fas fa-route mr-2"></i>
            使用流程
          </div>
          <h2 className="text-[clamp(1.8rem,3vw,2.5rem)] font-bold mb-6">
            3步轻松完成声音创作
          </h2>
          <p className="text-lg text-gray-600">
            简单几步，让AI用你的声音把文字读出来，高效完成声音内容创作
          </p>
        </div>

        <div className="relative">
          {/* 连接线 */}
          <div className="hidden md:block absolute top-1/2 left-0 right-0 h-1 bg-gray-200 -translate-y-1/2 z-0"></div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative z-10">
            {steps.map((step, index) => (
              <div key={index} className={`flex flex-col items-center text-center ${index === 1 ? 'mt-12 md:mt-0' : index === 2 ? 'mt-12 md:mt-0' : ''}`}>
                <div className="w-20 h-20 rounded-full bg-primary text-white flex items-center justify-center text-2xl font-bold mb-6 shadow-lg shadow-primary/20">
                  {step.number}
                </div>
                <div className="bg-gray-50 p-6 rounded-2xl w-full">
                  <h3 className="text-xl font-bold mb-3">{step.title}</h3>
                  <p className="text-gray-600 mb-4">{step.description}</p>
                  <img 
                    alt={step.title} 
                    className="w-full h-auto rounded-lg mb-4" 
                    src={step.image}
                  />
                  <span className="text-sm text-gray-500">{step.note}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 快速开始 */}
        <div className="mt-20 bg-gradient-to-r from-primary to-secondary rounded-3xl overflow-hidden shadow-2xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 items-center">
            <div className="hidden lg:block relative h-full min-h-[300px]">
              <img 
                alt="开始使用声线" 
                className="absolute inset-0 w-full h-full object-cover" 
                src="/images/quick-start.png"
              />
            </div>
            <div className="p-8 lg:p-12 text-white">
              <h3 className="text-3xl font-bold mb-4">立即开始创作</h3>
              <p className="text-lg mb-6 opacity-90">
                加入数万创作者的行列，用AI声音技术让你的内容更生动
              </p>
              <button className="bg-white text-primary px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
                免费试用
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
