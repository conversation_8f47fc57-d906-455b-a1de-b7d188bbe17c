import React from 'react';

const Features: React.FC = () => {
  const features = [
    {
      icon: 'fas fa-magic',
      iconBg: 'bg-primary/10',
      iconColor: 'text-primary',
      title: '克隆快又准',
      description: '3秒钟复刻你的声音语气、情感细节全捕捉，相似度99%+，让AI成为你的声音分身。'
    },
    {
      icon: 'fas fa-file-alt',
      iconBg: 'bg-secondary/10',
      iconColor: 'text-secondary',
      title: '文字转语音超方便',
      description: '支持PDF、TXT一键提取文字，直接转音频，不用手动打字，效率翻倍，告别繁琐输入。'
    },
    {
      icon: 'fas fa-th-large',
      iconBg: 'bg-green-100',
      iconColor: 'text-green-500',
      title: '多场景全覆盖',
      description: '短视频配音、课程录制、有声书创作、企业播报... 生成内容无上限，长文本也能轻松搞定。'
    },
    {
      icon: 'fas fa-share-alt',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-500',
      title: '一键分享不费力',
      description: '生成的音频文件支持直接同步到社交平台，内容传播快人一步，让你的声音作品迅速扩散。'
    }
  ];

  const checklistItems = [
    {
      title: '99%+相似度',
      description: '专业级声音克隆，朋友也分辨不出差异'
    },
    {
      title: '情感丰富',
      description: '支持喜悦、悲伤、愤怒等多种情感表达'
    },
    {
      title: '多风格转换',
      description: '支持新闻播报、故事讲述、广告宣传等多种风格'
    }
  ];

  const fileTypes = [
    { icon: 'fas fa-file-pdf', color: 'text-red-500', label: 'PDF文档' },
    { icon: 'fas fa-file-alt', color: 'text-blue-500', label: 'TXT文本' },
    { icon: null, image: '/images/word-icon.png', label: '图片（开发中）' },
    { icon: 'fas fa-link', color: 'text-green-500', label: '网页链接（开发中）' }
  ];

  return (
    <section className="py-20 bg-white" id="features">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <div className="inline-block px-4 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
            <i className="fas fa-crown mr-2"></i>
            核心功能
          </div>
          <h2 className="text-[clamp(1.8rem,3vw,2.5rem)] font-bold mb-6">
            为什么选择声线AI声音工具
          </h2>
          <p className="text-lg text-gray-600">
            我们的AI声音克隆合成技术，让声音创作变得前所未有的简单高效
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="bg-gray-50 rounded-2xl p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              <div className={`w-14 h-14 rounded-xl ${feature.iconBg} flex items-center justify-center ${feature.iconColor} text-2xl mb-6`}>
                <i className={feature.icon}></i>
              </div>
              <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* 详细功能展示 */}
        <div className="mt-24 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="order-2 lg:order-1">
            <h3 className="text-3xl font-bold mb-6">
              完美复刻你的声音，情感细节全捕捉
            </h3>
            <p className="text-gray-600 mb-8">
              我们的AI声音克隆技术不仅能复制你的音色，还能精准捕捉语气、节奏和情感变化，让生成的语音听起来自然真实，就像你亲自朗读一样。
            </p>
            <div className="space-y-6">
              {checklistItems.map((item, index) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-green-500 mt-1 flex-shrink-0">
                    <i className="fas fa-check"></i>
                  </div>
                  <div>
                    <h4 className="font-bold text-lg mb-1">{item.title}</h4>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="order-1 lg:order-2 relative">
            <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl">
              {/* 波形动画 */}
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6">
                <div className="flex items-end justify-center gap-1 h-24">
                  {Array.from({ length: 20 }, (_, i) => (
                    <div 
                      key={i}
                      className="w-1 bg-white/70 rounded-t-sm animate-pulse" 
                      style={{ height: `${30 + Math.random() * 60}%` }}
                    ></div>
                  ))}
                </div>
              </div>
            </div>
            {/* 装饰元素 */}
            <div className="absolute -top-6 -right-6 w-32 h-32 bg-primary/20 rounded-full filter blur-2xl -z-10"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-secondary/20 rounded-full filter blur-2xl -z-10"></div>
          </div>
        </div>

        {/* 第二个详细功能 */}
        <div className="mt-24 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="relative">
            <div className="relative z-10 rounded-2xl overflow-hidden shadow-2xl"></div>
            {/* 装饰元素 */}
            <div className="absolute -top-6 -left-6 w-32 h-32 bg-green-100 rounded-full filter blur-2xl -z-10"></div>
            <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-blue-100 rounded-full filter blur-2xl -z-10"></div>
          </div>
          
          <div>
            <h3 className="text-3xl font-bold mb-6">
              多种文件格式支持，告别手动输入
            </h3>
            <p className="text-gray-600 mb-8">
              无论是PDF电子书还是TXT文档，声线都能一键提取文字内容，直接转换为语音，让你告别繁琐的复制粘贴和手动输入。
            </p>
            <div className="grid grid-cols-2 gap-4 mb-8">
              {fileTypes.map((type, index) => (
                <div key={index} className="bg-gray-50 p-4 rounded-xl flex items-center gap-3">
                  {type.icon ? (
                    <i className={`${type.icon} ${type.color} text-xl`}></i>
                  ) : (
                    <img alt={type.label} className="w-6 h-6 object-cover" src={type.image} />
                  )}
                  <span>{type.label}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;
