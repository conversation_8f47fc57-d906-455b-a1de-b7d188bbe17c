import React from 'react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-12 mb-12">
          <div className="lg:col-span-2">
            <div className="text-2xl font-bold flex items-center mb-6">
              <i className="fas fa-microphone-alt mr-2 text-primary"></i>
              <span>声线</span>
            </div>
            <p className="text-gray-400 mb-6 max-w-md">
              声线是一款专业AI声音克隆合成创作工具，支持快速精准克隆声音音色，把文本内容转成语音，满足多场景声音创作需求。
            </p>
          </div>
          <div>
            {/* 可以在这里添加更多链接 */}
          </div>
        </div>
        
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-500 text-sm mb-4 md:mb-0">
              © 2025 声线 AI 声音工具. 保留所有权利.
            </p>
            <div className="flex space-x-6">
              {/* 可以在这里添加社交媒体链接 */}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
