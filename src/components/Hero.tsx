import React from 'react';

const Hero: React.FC = () => {
  return (
    <section className="pt-32 pb-20 md:pt-40 md:pb-32 overflow-hidden relative">
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 -z-10"></div>
      
      {/* 装饰元素 */}
      <div className="absolute top-20 right-10 w-64 h-64 bg-primary/10 rounded-full filter blur-3xl"></div>
      <div className="absolute bottom-20 left-10 w-80 h-80 bg-secondary/10 rounded-full filter blur-3xl"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row items-center gap-12">
          <div className="lg:w-1/2 text-center lg:text-left">
            <div className="inline-block px-4 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium mb-6">
              <i className="fas fa-rocket mr-2"></i>
              AI声音创作新体验
            </div>
            
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight mb-6">
              让AI用
              <span className="bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                你的声音
              </span>
              把文字读出来
            </h1>
            
            <p className="text-lg md:text-xl text-gray-600 mb-8 max-w-xl">
              3秒钟复刻你的声音，文字秒变语音，1分钟读完1小时内容。完美复刻本人声线，无论是短视频配音、播客制作还是有声书创作，都能轻松搞定。
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 mb-10">
              {/* 可以在这里添加CTA按钮 */}
            </div>
          </div>
          
          <div className="lg:w-1/2 relative">
            <div className="relative z-10 animate-float">
              <div className="relative mx-auto">
                {/* 手机框架 */}
                <div className="w-80 h-[640px] mx-auto border-8 border-gray-800 rounded-[36px] shadow-2xl bg-gray-900 relative overflow-hidden">
                  {/* 手机顶部听筒 */}
                  <div className="absolute top-4 left-1/2 transform -translate-x-1/2 w-24 h-4 bg-gray-800 rounded-full"></div>

                  {/* 缩小后的应用界面 */}
                  <img
                    alt="声线AI声音工具界面"
                    className="absolute top-8 left-4 right-4 bottom-8 w-[calc(100%-2rem)] h-[calc(100%-4rem)] object-cover rounded-[20px]"
                    src="/images/app-interface.jpg"
                  />
                </div>
              </div>
              
              {/* 浮动元素1 */}
              <div className="absolute -top-6 -left-6 bg-white p-4 rounded-xl shadow-lg animate-pulse-slow">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center text-green-500">
                    <i className="fas fa-check text-xl"></i>
                  </div>
                  <div>
                    <p className="font-medium">克隆完成</p>
                    <p className="text-sm text-gray-500">相似度99.8%</p>
                  </div>
                </div>
              </div>
              
              {/* 浮动元素2 */}
              <div 
                className="absolute -bottom-8 -right-8 bg-white p-4 rounded-xl shadow-lg animate-pulse-slow" 
                style={{animationDelay: '1s'}}
              >
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-500">
                    <i className="fas fa-clock text-xl"></i>
                  </div>
                  <div>
                    <p className="font-medium">处理速度</p>
                    <p className="text-sm text-gray-500">1分钟/小时内容</p>
                  </div>
                </div>
              </div>
            </div>
            
            {/* 背景装饰 */}
            <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full bg-gradient-to-r from-primary/30 to-secondary/30 rounded-full filter blur-3xl -z-10"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
