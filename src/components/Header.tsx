import React, { useState, useEffect } from 'react';

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      window.scrollTo({
        top: element.offsetTop - 80,
        behavior: 'smooth'
      });
    }
    setIsMobileMenuOpen(false);
  };

  return (
    <header 
      className={`fixed w-full top-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-md' : 'bg-transparent'
      }`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-4">
          <div className="flex items-center">
            <div className="text-primary text-2xl font-bold flex items-center">
              <i className="fas fa-microphone-alt mr-2"></i>
              <span>声线</span>
            </div>
          </div>

          {/* 桌面导航 */}
          <nav className="hidden md:flex space-x-8">
            <button 
              onClick={() => scrollToSection('features')}
              className="text-dark hover:text-primary transition-colors font-medium"
            >
              功能特点
            </button>
            <button 
              onClick={() => scrollToSection('how-it-works')}
              className="text-dark hover:text-primary transition-colors font-medium"
            >
              使用方法
            </button>
            <button 
              onClick={() => scrollToSection('faq')}
              className="text-dark hover:text-primary transition-colors font-medium"
            >
              常见问题
            </button>
          </nav>

          <div className="flex items-center space-x-4">
            <a 
              href="javascript:void(0);"
              className="px-5 py-2 rounded-full bg-primary text-white hover:bg-primary/90 shadow-lg shadow-primary/20 transition-all transform hover:-translate-y-0.5"
            >
              下载APP
            </a>
            
            {/* 移动端菜单按钮 */}
            <button 
              className="md:hidden text-dark focus:outline-none"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <i className={`fas ${isMobileMenuOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
            </button>
          </div>
        </div>
      </div>

      {/* 移动端导航菜单 */}
      <div className={`${isMobileMenuOpen ? 'block' : 'hidden'} md:hidden bg-white shadow-lg absolute w-full`}>
        <div className="container mx-auto px-4 py-3 space-y-3">
          <button 
            onClick={() => scrollToSection('features')}
            className="block py-2 text-dark hover:text-primary transition-colors font-medium w-full text-left"
          >
            功能特点
          </button>
          <button 
            onClick={() => scrollToSection('how-it-works')}
            className="block py-2 text-dark hover:text-primary transition-colors font-medium w-full text-left"
          >
            使用方法
          </button>
          <button 
            onClick={() => scrollToSection('faq')}
            className="block py-2 text-dark hover:text-primary transition-colors font-medium w-full text-left"
          >
            常见问题
          </button>
          <a 
            href="javascript:void(0);"
            className="block py-2 text-dark hover:text-primary transition-colors font-medium"
          >
            登录
          </a>
        </div>
      </div>
    </header>
  );
};

export default Header;
