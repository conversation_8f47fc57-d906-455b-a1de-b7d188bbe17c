import React from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import Features from './components/Features';
import HowItWorks from './components/HowItWorks';
import Footer from './components/Footer';
import BackToTop from './components/BackToTop';

function App() {
  return (
    <div className="min-h-screen font-sans bg-light text-dark antialiased">
      <Header />
      <main>
        <Hero />
        <Features />
        <HowItWorks />
        {/* FAQ section placeholder */}
        <section className="py-20 bg-white" id="faq">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center max-w-3xl mx-auto mb-16">
              <div className="inline-block px-4 py-1.5 bg-primary/10 text-primary rounded-full text-sm font-medium mb-4">
                <i className="fas fa-question-circle mr-2"></i>
                常见问题
              </div>
              <h2 className="text-[clamp(1.8rem,3vw,2.5rem)] font-bold mb-6">
                常见问题解答
              </h2>
              <p className="text-lg text-gray-600">
                这里是一些用户经常询问的问题
              </p>
            </div>
          </div>
        </section>
      </main>
      <Footer />
      <BackToTop />
    </div>
  );
}

export default App;
